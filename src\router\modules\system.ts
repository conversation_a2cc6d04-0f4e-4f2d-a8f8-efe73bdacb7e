import type { Route } from '../index.type'
import Layout from '@/layout/index.vue'
import { createNameComponent } from '../createNode'
const route: Route[] = [
  {
    path: '/system',
    component: Layout,
    redirect: '/404',
    hideMenu: true,
    meta: { title: '跳转失败' },
    children: [
      {
        path: '/404',
        component: createNameComponent(() => import('@/views/system/404.vue')),
        meta: { title: '页面不存在', hideTabs: true }
      },
      {
        path: '/401',
        component: createNameComponent(() => import('@/views/system/401.vue')),
        meta: { title: 'message.menu.system.401', hideTabs: true }
      },
      {
        path: '/redirect/:path(.*)',
        component: createNameComponent(() => import('@/views/system/redirect.vue')),
        meta: { title: 'message.menu.system.redirect', hideTabs: true }
      }
    ]
  },
  {
    path: '/login',
    component: createNameComponent(() => import('@/views/system/login.vue')),
    hideMenu: true,
    meta: { title: 'message.system.login', hideTabs: true }
  },
  {
    path: '/qr-scanner',
    component: createNameComponent(() => import('@/views/qr-scanner/index.vue')),
    hideMenu: true,
    meta: { title: '扫码', hideTabs: true }
  },
  {
    path: '/qr-scanner/product-detail',
    component: createNameComponent(() => import('@/views/qr-scanner/product-detail.vue')),
    hideMenu: true,
    meta: { title: '商品详情', hideTabs: true }
  },

]

export default route
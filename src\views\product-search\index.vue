<template>
  <!-- 全局 Toast 提示 -->
  <div v-if="toast.show" class="fixed top-[1.5vh] left-1/2 -translate-x-1/2 z-50 w-auto max-w-sm rounded-2xl bg-gray-900/95 dark:bg-gray-50/95 text-white dark:text-black shadow-2xl backdrop-blur-sm p-[1.25vh] animate-fade-in-down border border-gray-700/20 dark:border-gray-200/20">
    <p class="font-bold text-[1rem]">{{ toast.title }}</p>
    <p class="text-[0.875rem] opacity-90 mt-[0.25vh]">{{ toast.description }}</p>
  </div>

  <div class="w-full min-h-screen bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200 dark:from-gray-950 dark:via-gray-900 dark:to-gray-800 font-sans">
    <div class="mx-auto max-w-md bg-white/80 dark:bg-black/80 backdrop-blur-sm">
      
      <!-- 头部搜索区域 -->
      <header class="sticky top-0 z-10 bg-white/95 dark:bg-black/95 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-700/50 p-[1.5vh]">
        <div class="flex items-center gap-[1vw] mb-[1vh]">
          <!-- 返回按钮 -->
          <button @click="goBack" class="w-[2.5rem] h-[2.5rem] rounded-xl bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 flex items-center justify-center transition-all duration-200">
            <svg class="w-[1.25rem] h-[1.25rem] text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
          </button>
          
          <h1 class="text-[1.25rem] font-bold text-gray-900 dark:text-white">产品搜索</h1>
        </div>
        
        <!-- 搜索输入框 -->
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <input
            v-model="searchKeyword"
            @input="handleSearch"
            type="text"
            placeholder="输入产品名称或编码搜索..."
            class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          />
        </div>
      </header>

      <!-- 主体内容区域 -->
      <main class="p-[1.5vh] pb-[8vh]">
        <!-- 加载状态 -->
        <div v-if="loading" class="flex flex-col items-center justify-center py-[4vh] space-y-[1vh]">
          <div class="relative">
            <div class="w-[3rem] h-[3rem] rounded-full bg-gradient-to-r from-blue-500 to-purple-600 animate-pulse"></div>
            <svg class="absolute inset-0 h-[3rem] w-[3rem] animate-spin text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          </div>
          <span class="text-gray-600 dark:text-gray-400 font-medium">搜索中...</span>
        </div>

        <!-- 空状态 -->
        <div v-else-if="!searchKeyword.trim()" class="flex flex-col items-center justify-center py-[6vh] text-center">
          <div class="w-[4rem] h-[4rem] rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center mb-[2vh]">
            <svg class="w-[2rem] h-[2rem] text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <h3 class="text-[1.125rem] font-semibold text-gray-900 dark:text-white mb-[0.5vh]">开始搜索产品</h3>
          <p class="text-gray-600 dark:text-gray-400">输入产品名称或编码来查找产品</p>
        </div>

        <!-- 无搜索结果 -->
        <div v-else-if="!loading && productList.length === 0" class="flex flex-col items-center justify-center py-[6vh] text-center">
          <div class="w-[4rem] h-[4rem] rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-[2vh]">
            <svg class="w-[2rem] h-[2rem] text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.291-1.007-5.691-2.709M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
            </svg>
          </div>
          <h3 class="text-[1.125rem] font-semibold text-gray-900 dark:text-white mb-[0.5vh]">未找到相关产品</h3>
          <p class="text-gray-600 dark:text-gray-400">请尝试其他关键词</p>
        </div>

        <!-- 产品列表 -->
        <div v-else class="space-y-[1vh]">
          <div
            v-for="product in productList"
            :key="product.id"
            class="group rounded-2xl bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900/90 dark:to-gray-800/50 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-200/50 dark:border-gray-700/50 backdrop-blur-sm"
          >
            <div class="p-[1.5vh]">
              <!-- 产品信息 -->
              <div class="flex items-center gap-[1.25vw] mb-[1vh]">
                <div class="h-[4rem] w-[4rem] rounded-xl bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 overflow-hidden shadow-md border border-gray-200/50 dark:border-gray-600/50">
                  <imagePreview :imgurl="product.image" :width="64" :height="64" :type="1" />
                </div>
                <div class="flex-1 space-y-[0.25vh]">
                  <h3 class="text-[1rem] font-bold text-gray-900 dark:text-white leading-tight">{{ product.productName || '未知产品' }}</h3>
                  <p class="text-[0.75rem] text-gray-600 dark:text-gray-400">编码: {{ product.attrValueNo || 'N/A' }}</p>
                  <p class="text-[0.75rem] text-gray-600 dark:text-gray-400">规格: {{ product.unitGroupStr || '暂无规格信息' }}</p>
                </div>
              </div>
              
              <!-- 操作按钮 -->
              <button
                @click="handleProductSelect(product)"
                class="w-full h-[2.75rem] rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold transition-all duration-200 flex items-center justify-center gap-[0.5vw] transform hover:scale-[1.02] active:scale-[0.98]"
              >
                <svg class="w-[1rem] h-[1rem]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                </svg>
                出库/入库
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { getAttrValueList } from '@/api/product'
import imagePreview from '@/components/imagePreview/index.vue'

const router = useRouter()

// 状态定义
const searchKeyword = ref('')
const loading = ref(false)
const productList = ref<any[]>([])

// Toast 提示状态
const toast = reactive({
  show: false,
  title: '',
  description: ''
})

// 搜索防抖定时器
let searchTimer: NodeJS.Timeout | null = null

// 显示Toast提示
function showToastNotification(title: string, description: string) {
  toast.title = title
  toast.description = description
  toast.show = true
  setTimeout(() => {
    toast.show = false
  }, 3000)
}

// 处理搜索
async function handleSearch() {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  
  searchTimer = setTimeout(async () => {
    if (!searchKeyword.value.trim()) {
      productList.value = []
      return
    }
    
    await performSearch()
  }, 500) // 500ms 防抖
}

// 执行搜索
async function performSearch() {
  try {
    loading.value = true
    
    const params = {
      page: 1,
      limit: 50,
      keywords: searchKeyword.value.trim()
    }
    
    const response = await getAttrValueList(params)
    productList.value = response.list || []
    
  } catch (error: any) {
    console.error('搜索产品失败:', error)
    showToastNotification('搜索失败', error.message || '请稍后重试')
    productList.value = []
  } finally {
    loading.value = false
  }
}

// 处理产品选择
function handleProductSelect(product: any) {
  // 跳转到产品详情页面，传递产品编码
  router.push({
    path: '/qr-scanner/product-detail',
    query: { 
      id: product.attrValueNo || product.barCode,
      from: 'search' // 标记来源为搜索
    }
  })
}

// 返回上一页
function goBack() {
  router.back()
}
</script>

<style scoped>
/* 自定义动画 */
@keyframes fade-in-down {
  0% {
    opacity: 0;
    transform: translateY(-10px) translateX(-50%);
  }
  100% {
    opacity: 1;
    transform: translateY(0) translateX(-50%);
  }
}

.animate-fade-in-down {
  animation: fade-in-down 0.3s ease-out;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* 深色模式滚动条 */
.dark ::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.7);
}
</style>
